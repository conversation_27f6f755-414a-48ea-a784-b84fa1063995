package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"strings"
	"syscall"
	"time"

	"apexstream/internal/api"
	"apexstream/internal/processor"
	"apexstream/internal/queue"
	"apexstream/internal/storage"
	"apexstream/internal/websocket"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

type Config struct {
	Port          string
	Environment   string
	VideoDir      string
	MaxFileSize   int64
	EnableFFmpeg  bool
	StorageType   string
	EnableStorage bool
	R2Endpoint    string
	R2AccessKey   string
	R2SecretKey   string
	R2BucketName  string
	R2PublicURL   string
	LocalBaseDir  string
	LocalBaseURL  string
	// Configurações de autenticação
	EnableAuth bool
	APIKeys    string // Lista de API keys separadas por vírgula
	// Configurações de fila
	EnableQueue     bool
	RedisHost       string
	RedisPort       string
	RedisPassword   string
	RedisDB         int
	RedisPoolSize   int
	WorkerPoolSize  int
	JobTimeout      time.Duration
	MaxRetries      int
	RetryDelay      time.Duration
	CleanupInterval time.Duration
}

func loadConfig() *Config {
	config := &Config{
		Port:          getEnv("PORT", "8080"),
		Environment:   getEnv("ENVIRONMENT", "development"),
		VideoDir:      getEnv("VIDEO_DIR", "./videos"),
		MaxFileSize:   getEnvInt64("MAX_FILE_SIZE", 100*1024*1024), // 100MB
		EnableFFmpeg:  getEnvBool("ENABLE_FFMPEG", true),           // FFmpeg habilitado por padrão
		StorageType:   getEnv("STORAGE_TYPE", "r2"),                // local ou r2
		EnableStorage: getEnvBool("ENABLE_STORAGE", true),          // Storage habilitado por padrão
		R2Endpoint:    getEnv("R2_ENDPOINT", ""),
		R2AccessKey:   getEnv("R2_ACCESS_KEY", ""),
		R2SecretKey:   getEnv("R2_SECRET_KEY", ""),
		R2BucketName:  getEnv("R2_BUCKET_NAME", ""),
		R2PublicURL:   getEnv("R2_PUBLIC_URL", ""),
		LocalBaseDir:  getEnv("LOCAL_STORAGE_DIR", "./storage"),
		LocalBaseURL:  getEnv("LOCAL_STORAGE_URL", "/files"),
		// Configurações de autenticação
		EnableAuth: getEnvBool("ENABLE_AUTH", false), // Autenticação desabilitada por padrão
		APIKeys:    getEnv("API_KEYS", ""),           // Lista de API keys separadas por vírgula
		// Configurações de fila
		EnableQueue:     getEnvBool("ENABLE_QUEUE", true),
		RedisHost:       getEnv("REDIS_HOST", "localhost"),
		RedisPort:       getEnv("REDIS_PORT", "6379"),
		RedisPassword:   getEnv("REDIS_PASSWORD", ""),
		RedisDB:         getEnvInt("REDIS_DB", 0),
		RedisPoolSize:   getEnvInt("REDIS_POOL_SIZE", 10),
		WorkerPoolSize:  getEnvInt("WORKER_POOL_SIZE", 3),
		JobTimeout:      getEnvDuration("JOB_TIMEOUT", 300*time.Second),
		MaxRetries:      getEnvInt("MAX_RETRIES", 3),
		RetryDelay:      getEnvDuration("RETRY_DELAY", 30*time.Second),
		CleanupInterval: getEnvDuration("QUEUE_CLEANUP_INTERVAL", 60*time.Second),
	}

	return config
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt64(key string, defaultValue int64) int64 {
	if value := os.Getenv(key); value != "" {
		if parsed := parseInt64(value); parsed > 0 {
			return parsed
		}
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		switch strings.ToLower(value) {
		case "true", "1", "yes", "on":
			return true
		case "false", "0", "no", "off":
			return false
		}
	}
	return defaultValue
}

func parseInt64(s string) int64 {
	var result int64
	for _, char := range s {
		if char >= '0' && char <= '9' {
			result = result*10 + int64(char-'0')
		} else {
			return 0
		}
	}
	return result
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if parsed := parseInt(value); parsed > 0 {
			return parsed
		}
	}
	return defaultValue
}

func parseInt(s string) int {
	var result int
	for _, char := range s {
		if char >= '0' && char <= '9' {
			result = result*10 + int(char-'0')
		} else {
			return 0
		}
	}
	return result
}

func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if parsed, err := time.ParseDuration(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func initializeQueueSystem(config *Config) (*queue.QueueManager, *queue.WorkerPool, error) {
	if !config.EnableQueue {
		log.Printf("🚫 Sistema de filas desabilitado na configuração")
		return nil, nil, nil
	}

	// Configurar Redis
	redisConfig := &queue.RedisConfig{
		Host:         config.RedisHost,
		Port:         config.RedisPort,
		Password:     config.RedisPassword,
		DB:           config.RedisDB,
		PoolSize:     config.RedisPoolSize,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
	}

	redisClient, err := queue.NewRedisClient(redisConfig)
	if err != nil {
		return nil, nil, fmt.Errorf("erro ao conectar com Redis: %v", err)
	}

	// Configurar Queue Manager
	queueConfig := &queue.QueueConfig{
		DefaultTimeout:    config.JobTimeout,
		RetryDelay:        config.RetryDelay,
		MaxRetries:        config.MaxRetries,
		DeadLetterEnabled: true,
		CleanupInterval:   config.CleanupInterval,
	}

	queueManager := queue.NewQueueManager(redisClient, queueConfig)

	// Inicializar processador de vídeo
	videoProcessor := processor.NewVideoProcessor(&processor.ProcessorConfig{
		FFmpegPath:    "ffmpeg",
		OutputDir:     filepath.Join(config.VideoDir, "processed"),
		TempDir:       filepath.Join(config.VideoDir, "temp"),
		SegmentTime:   10,
		PlaylistType:  "vod",
		VideoCodec:    "libx264",
		AudioCodec:    "aac",
		VideoBitrates: []int{1000, 2000, 4000},
	})

	// Inicializar storage provider
	storageProvider, err := initializeStorageProvider(config)
	if err != nil {
		log.Printf("⚠️  Aviso: Falha ao inicializar storage provider: %v", err)
		storageProvider = nil
	}

	// Configurar Worker Pool
	workerConfig := &queue.WorkerConfig{
		PoolSize:          config.WorkerPoolSize,
		JobTimeout:        config.JobTimeout,
		HeartbeatInterval: 30 * time.Second,
		ShutdownTimeout:   60 * time.Second,
	}

	workerPool := queue.NewWorkerPool(queueManager, videoProcessor, storageProvider, workerConfig)

	log.Printf("✅ Sistema de filas inicializado com sucesso")
	log.Printf("  Redis: %s:%s", config.RedisHost, config.RedisPort)
	log.Printf("  Workers: %d", config.WorkerPoolSize)
	log.Printf("  Job Timeout: %v", config.JobTimeout)

	return queueManager, workerPool, nil
}

func initializeStorageProvider(config *Config) (storage.StorageProvider, error) {
	if !config.EnableStorage {
		log.Printf("🚫 Storage desabilitado na configuração")
		return nil, nil
	}

	storageProvider, err := storage.NewStorageProviderFromEnv(
		config.StorageType,
		// R2 config
		config.R2Endpoint,
		config.R2AccessKey,
		config.R2SecretKey,
		config.R2BucketName,
		config.R2PublicURL,
		// Local config
		config.LocalBaseDir,
		config.LocalBaseURL,
	)
	if err != nil {
		return nil, fmt.Errorf("erro ao inicializar storage provider: %v", err)
	}

	log.Printf("✅ Storage provider inicializado com sucesso")
	return storageProvider, nil
}

func setupRouter(config *Config) (*gin.Engine, *queue.QueueManager, *queue.WorkerPool, *websocket.Hub, error) {
	// Processar API keys
	var apiKeys []string
	if config.APIKeys != "" {
		// Dividir por vírgula e limpar espaços
		keys := strings.Split(config.APIKeys, ",")
		for _, key := range keys {
			trimmed := strings.TrimSpace(key)
			if trimmed != "" {
				apiKeys = append(apiKeys, trimmed)
			}
		}
	}

	// Inicializar WebSocket Hub
	wsHub := websocket.NewHub(nil)
	go wsHub.Run()
	log.Printf("✅ WebSocket Hub inicializado")

	// Inicializar sistema de filas se habilitado
	var queueManager *queue.QueueManager
	var workerPool *queue.WorkerPool
	var err error

	if config.EnableQueue {
		queueManager, workerPool, err = initializeQueueSystem(config)
		if err != nil {
			return nil, nil, nil, nil, fmt.Errorf("falha ao inicializar sistema de filas: %v", err)
		}

		// Configurar o WebSocket Hub como progress reporter no worker pool
		if workerPool != nil {
			workerPool.SetProgressReporter(wsHub)
		}
	}

	// Configurar router
	if config.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// Health check básico
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":    "ok",
			"timestamp": time.Now().Unix(),
			"service":   "apexstream",
		})
	})

	// Endpoint raiz para informações da API
	router.GET("/", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"service":   "ApexStream Video Processing API",
			"version":   "1.0.0",
			"status":    "running",
			"timestamp": time.Now().Unix(),
			"health":    "/health",
			"api":       "/api/v1",
		})
	})

	// Configurar rotas da API
	v1 := router.Group("/api/v1")

	// Configuração de autenticação
	authConfig := &api.AuthConfig{
		APIKeys:    apiKeys,
		EnableAuth: config.EnableAuth,
	}

	if config.EnableQueue && queueManager != nil {
		// Usar handlers com fila
		queueConfig := &api.QueueConfig{
			VideoDir:      config.VideoDir,
			MaxFileSize:   config.MaxFileSize,
			ProcessedDir:  filepath.Join(config.VideoDir, "processed"),
			QueueManager:  queueManager,
			EnableStorage: config.EnableStorage,
			StorageType:   config.StorageType,
		}

		// Rotas com fila
		v1.POST("/upload", api.APIKeyAuthMiddleware(authConfig), api.QueueUploadHandler(queueConfig))
		v1.GET("/jobs/:id", api.APIKeyAuthMiddleware(authConfig), api.JobStatusHandler(queueConfig))
		v1.GET("/jobs", api.APIKeyAuthMiddleware(authConfig), api.ListJobsHandler(queueConfig))
		v1.GET("/queue/stats", api.APIKeyAuthMiddleware(authConfig), api.QueueStatsHandler(queueConfig))
		v1.POST("/jobs/:id/retry", api.APIKeyAuthMiddleware(authConfig), api.RetryJobHandler(queueConfig))
		v1.DELETE("/queue/:queue/purge", api.APIKeyAuthMiddleware(authConfig), api.PurgeQueueHandler(queueConfig))
		v1.POST("/queue/cleanup", api.APIKeyAuthMiddleware(authConfig), api.CleanupHandler(queueConfig))
		v1.GET("/workers/stats", api.APIKeyAuthMiddleware(authConfig), api.WorkerStatsHandler(workerPool))
		v1.GET("/health/queue", api.HealthCheckHandler(queueConfig, workerPool))

		log.Printf("✅ Router configurado com sistema de filas")
	} else {
		// Usar handlers tradicionais (fallback)
		storageProvider, err := initializeStorageProvider(config)
		if err != nil {
			return nil, nil, nil, nil, fmt.Errorf("falha ao inicializar storage: %v", err)
		}

		routerConfig := &api.RouterConfig{
			VideoDir:        config.VideoDir,
			MaxFileSize:     config.MaxFileSize,
			Environment:     config.Environment,
			ProcessedDir:    filepath.Join(config.VideoDir, "processed"),
			EnableFFmpeg:    config.EnableFFmpeg,
			StorageProvider: storageProvider,
			EnableStorage:   config.EnableStorage,
			APIKeys:         apiKeys,
			EnableAuth:      config.EnableAuth,
		}

		// Configurar rotas tradicionais
		return api.SetupRouter(routerConfig), nil, nil, wsHub, nil
	}

	// Configurar rotas WebSocket
	wsConfig := &api.WebSocketConfig{
		Hub: wsHub,
	}

	// Adicionar rotas WebSocket
	v1.GET("/ws/:jobId", api.WebSocketProgressHandler(wsConfig))
	v1.GET("/ws/stats", api.APIKeyAuthMiddleware(authConfig), api.WebSocketStatsHandler(wsConfig))
	v1.GET("/ws/health", api.WebSocketHealthHandler(wsConfig))
	v1.POST("/ws/broadcast", api.APIKeyAuthMiddleware(authConfig), api.BroadcastMessageHandler(wsConfig))
	v1.POST("/ws/:jobId/progress", api.APIKeyAuthMiddleware(authConfig), api.SendJobProgressHandler(wsConfig))

	return router, queueManager, workerPool, wsHub, nil
}

func createVideoDirectory(videoDir string) error {
	if _, err := os.Stat(videoDir); os.IsNotExist(err) {
		log.Printf("Creating video directory: %s", videoDir)
		return os.MkdirAll(videoDir, 0755)
	}
	return nil
}

func validateConfig(config *Config) error {
	if config.Port == "" {
		return fmt.Errorf("PORT is required")
	}

	if config.VideoDir == "" {
		return fmt.Errorf("VIDEO_DIR is required")
	}

	if err := createVideoDirectory(config.VideoDir); err != nil {
		return fmt.Errorf("failed to create video directory: %w", err)
	}

	log.Printf("Configuration loaded successfully:")
	log.Printf("  Port: %s", config.Port)
	log.Printf("  Environment: %s", config.Environment)
	log.Printf("  Video Directory: %s", config.VideoDir)
	log.Printf("  Max File Size: %d bytes", config.MaxFileSize)
	log.Printf("  FFmpeg Enabled: %t", config.EnableFFmpeg)
	log.Printf("  Storage Enabled: %t", config.EnableStorage)
	log.Printf("  Storage Type: %s", config.StorageType)

	if config.EnableStorage {
		if config.StorageType == "r2" {
			log.Printf("  R2 Configured: %t", config.R2Endpoint != "" && config.R2AccessKey != "")
		} else {
			log.Printf("  Local Storage Dir: %s", config.LocalBaseDir)
		}
	}

	return nil
}

func main() {
	log.Println("Starting ApexStream Video Processing Server...")

	// Carregar variáveis de ambiente do arquivo .env
	if err := godotenv.Load(); err != nil {
		log.Printf("Aviso: Não foi possível carregar o arquivo .env: %v", err)
		log.Println("Continuando com variáveis de ambiente do sistema...")
	} else {
		log.Println("✅ Arquivo .env carregado com sucesso")
	}

	config := loadConfig()

	if err := validateConfig(config); err != nil {
		log.Fatalf("Configuration validation failed: %v", err)
	}

	router, queueManager, workerPool, wsHub, err := setupRouter(config)
	if err != nil {
		log.Fatalf("Failed to setup router: %v", err)
	}

	// Log WebSocket status
	if wsHub != nil {
		log.Printf("✅ WebSocket system initialized and ready")
	}

	server := &http.Server{
		Addr:         ":" + config.Port,
		Handler:      router,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// Iniciar worker pool se disponível
	if workerPool != nil {
		if err := workerPool.Start(); err != nil {
			log.Fatalf("Failed to start worker pool: %v", err)
		}
	}

	// Iniciar servidor HTTP
	go func() {
		log.Printf("🚀 Server starting on port %s", config.Port)
		log.Printf("Health check available at: http://localhost:%s/health", config.Port)

		if config.EnableQueue && queueManager != nil {
			log.Printf("✅ Queue system enabled with %d workers", config.WorkerPoolSize)
			log.Printf("Queue endpoints available at: http://localhost:%s/api/v1/jobs", config.Port)
			log.Printf("Queue stats available at: http://localhost:%s/api/v1/queue/stats", config.Port)
			log.Printf("Worker stats available at: http://localhost:%s/api/v1/workers/stats", config.Port)
		} else {
			log.Printf("📝 Using synchronous processing")
			log.Printf("Upload endpoint: http://localhost:%s/api/v1/upload", config.Port)
		}

		log.Printf("🌟 ApexStream server is ready to accept connections on port %s", config.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Aguardar sinal de shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	// Graceful shutdown
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// Parar worker pool primeiro
	if workerPool != nil {
		log.Println("Stopping worker pool...")
		if err := workerPool.Stop(); err != nil {
			log.Printf("Error stopping worker pool: %v", err)
		} else {
			log.Println("Worker pool stopped successfully")
		}
	}

	// Parar servidor HTTP
	log.Println("Stopping HTTP server...")
	if err := server.Shutdown(shutdownCtx); err != nil {
		log.Printf("Server forced to shutdown: %v", err)
	} else {
		log.Println("Server shutdown completed")
	}

	log.Println("Application shutdown completed")
}
