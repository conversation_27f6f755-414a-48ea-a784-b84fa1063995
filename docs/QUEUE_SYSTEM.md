# Sistema de Filas ApexStream

Este documento descreve o sistema de filas assíncronas implementado no ApexStream para processamento de vídeos em background.

## 📋 Visão Geral

O sistema de filas transforma o processamento síncrono original em um sistema assíncrono robusto que oferece:

- **Processamento Assíncrono**: Upload rápido com processamento em background
- **Escalabilidade**: Múltiplos workers processando em paralelo
- **Confiabilidade**: Retry automático e dead letter queue
- **Monitoramento**: APIs completas para acompanhar status e métricas
- **Graceful Shutdown**: Finalização segura sem perda de jobs

## 🏗️ Arquitetura

```
Cliente → Upload API → Redis Queue → Worker Pool → FFmpeg → Storage → Callback
```

### Componentes Principais

1. **Redis**: Broker de mensagens para as filas
2. **Queue Manager**: Gerencia operações de enqueue/dequeue
3. **Worker Pool**: Pool de workers para processamento paralelo
4. **Job System**: Sistema de jobs com status e metadados
5. **Metrics & Logging**: Sistema de monitoramento e logs estruturados

## 🚀 Como Usar

### 1. Configuração via Variáveis de Ambiente

```bash
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_POOL_SIZE=10

# Worker Pool Configuration
WORKER_POOL_SIZE=3
JOB_TIMEOUT=300s
MAX_RETRIES=3
RETRY_DELAY=30s

# Queue Configuration
ENABLE_QUEUE=true
QUEUE_CLEANUP_INTERVAL=60s
```

### 2. Upload de Vídeo

**Endpoint**: `POST /api/v1/upload`

**Headers**:
```
Authorization: Bearer YOUR_API_KEY
Content-Type: multipart/form-data
```

**Body**:
```
video: [arquivo de vídeo]
```

**Resposta**:
```json
{
  "success": true,
  "job_id": "job_a1b2c3d4e5f6",
  "message": "Upload concluído com sucesso. Processamento iniciado.",
  "filename": "video.mp4",
  "size": 52428800,
  "timestamp": **********,
  "status": "pending"
}
```

### 3. Consultar Status do Job

**Endpoint**: `GET /api/v1/jobs/{job_id}`

**Resposta**:
```json
{
  "success": true,
  "job": {
    "id": "job_a1b2c3d4e5f6",
    "status": "completed",
    "priority": 5,
    "input_file": "/app/videos/upload_abc123_video.mp4",
    "output_dir": "/app/videos/processed/job_a1b2c3d4e5f6",
    "config": {
      "segment_time": 10,
      "playlist_type": "vod",
      "video_codec": "libx264",
      "audio_codec": "aac",
      "enable_storage": true,
      "storage_type": "r2"
    },
    "result": {
      "success": true,
      "playlist_url": "https://cdn.example.com/videos/job_a1b2c3d4e5f6/playlist.m3u8",
      "segment_urls": [
        "https://cdn.example.com/videos/job_a1b2c3d4e5f6/segment_000.ts",
        "https://cdn.example.com/videos/job_a1b2c3d4e5f6/segment_001.ts"
      ],
      "duration": 120.5,
      "processing_time": "45s",
      "segment_count": 12,
      "storage_uploaded": true,
      "storage_provider": "r2"
    },
    "created_at": "2024-01-15T10:30:00Z",
    "completed_at": "2024-01-15T10:30:45Z"
  }
}
```

## 📊 APIs de Monitoramento

### Estatísticas das Filas

**Endpoint**: `GET /api/v1/queue/stats`

```json
{
  "success": true,
  "stats": {
    "high": 0,
    "normal": 2,
    "low": 1,
    "processing": 1,
    "completed": 45,
    "failed": 2,
    "dead_letter": 0,
    "total_pending": 3,
    "total_processing": 1,
    "total_completed": 45,
    "total_failed": 2
  }
}
```

### Estatísticas dos Workers

**Endpoint**: `GET /api/v1/workers/stats`

```json
{
  "success": true,
  "pool_stats": {
    "total_workers": 3,
    "active_workers": 1,
    "total_processed": 47,
    "total_succeeded": 45,
    "total_failed": 2,
    "started_at": "2024-01-15T10:00:00Z",
    "uptime": "2h30m15s"
  },
  "worker_stats": [
    {
      "jobs_processed": 16,
      "jobs_succeeded": 15,
      "jobs_failed": 1,
      "last_job_at": "2024-01-15T12:25:30Z",
      "started_at": "2024-01-15T10:00:00Z",
      "is_active": true,
      "current_job_id": "job_xyz789",
      "processing_time": "12m30s"
    }
  ]
}
```

### Health Check do Sistema

**Endpoint**: `GET /api/v1/health/queue`

```json
{
  "success": true,
  "timestamp": **********,
  "service": "apexstream-queue",
  "redis": {
    "status": "healthy",
    "stats": {
      "total_pending": 3,
      "total_processing": 1
    }
  },
  "worker_pool": {
    "status": "healthy",
    "total_workers": 3,
    "active_workers": 1,
    "uptime": "2h30m15s"
  },
  "directories": {
    "video_dir": {
      "status": "healthy",
      "path": "/app/videos"
    },
    "processed_dir": {
      "status": "healthy",
      "path": "/app/videos/processed"
    }
  }
}
```

## 🔧 Operações Administrativas

### Listar Jobs de uma Fila

**Endpoint**: `GET /api/v1/jobs?queue=normal&limit=10&offset=0`

### Reprocessar Job Falhado

**Endpoint**: `POST /api/v1/jobs/{job_id}/retry`

### Limpar Fila

**Endpoint**: `DELETE /api/v1/queue/{queue_name}/purge`

Filas disponíveis: `high`, `normal`, `low`, `completed`, `failed`, `dead_letter`

### Limpeza de Filas (Sistema de Manutenção)

**Endpoint**: `POST /api/v1/queue/cleanup`

**Parâmetros de Query**:
- `type`: Tipo de limpeza a ser executada

**Tipos de Limpeza Disponíveis**:

#### 1. Limpeza de Jobs Expirados
```bash
POST /api/v1/queue/cleanup?type=expired
```

Remove jobs que estão na fila de processamento há mais tempo que o timeout configurado.

**Resposta**:
```json
{
  "success": true,
  "message": "Limpeza de jobs expirados concluída"
}
```

#### 2. Limpeza de Jobs Órfãos
```bash
POST /api/v1/queue/cleanup?type=orphaned
```

Remove jobs corrompidos ou inválidos da fila de processamento que podem estar causando problemas nas estatísticas.

**Resposta**:
```json
{
  "success": true,
  "message": "Limpeza de jobs órfãos concluída"
}
```

#### 3. Limpeza Completa
```bash
POST /api/v1/queue/cleanup?type=all
```

Executa ambas as limpezas (expirados e órfãos).

**Resposta**:
```json
{
  "success": true,
  "message": "Limpeza completa concluída"
}
```

## 📈 Status dos Jobs

| Status | Descrição |
|--------|-----------|
| `pending` | Job na fila aguardando processamento |
| `processing` | Job sendo processado por um worker |
| `completed` | Job processado com sucesso |
| `failed` | Job falhou após todas as tentativas |
| `retrying` | Job sendo reagendado para nova tentativa |
| `cancelled` | Job cancelado manualmente |

## 🔄 Sistema de Retry

- **Tentativas**: Configurável via `MAX_RETRIES` (padrão: 3)
- **Delay**: Configurável via `RETRY_DELAY` (padrão: 30s)
- **Dead Letter Queue**: Jobs que falharam todas as tentativas
- **Cleanup**: Limpeza automática de jobs expirados

## 🧹 Sistema de Limpeza Automática

O sistema inclui um worker de limpeza que executa automaticamente em intervalos regulares para manter a integridade das filas.

### Limpeza Automática

**Configuração**:
```bash
QUEUE_CLEANUP_INTERVAL=60s  # Intervalo entre limpezas (padrão: 1 minuto)
```

**Operações Executadas**:

1. **A cada ciclo** (configurado por `QUEUE_CLEANUP_INTERVAL`):
   - Remove jobs expirados da fila de processamento
   - Move jobs expirados para retry ou dead letter queue

2. **A cada 5 ciclos** (menos frequente):
   - Remove jobs corrompidos ou inválidos
   - Reconstrói a fila de processamento removendo entradas problemáticas

### Problemas Resolvidos pelo Sistema de Limpeza

#### Jobs Presos em "processing"
- **Causa**: Jobs que foram iniciados mas o worker falhou ou foi interrompido
- **Solução**: Limpeza automática detecta jobs expirados e os reprocessa
- **Método**: Comparação do timestamp de início com o timeout configurado

#### Estatísticas Incorretas
- **Causa**: Jobs corrompidos na serialização/deserialização JSON
- **Solução**: Limpeza de órfãos remove entradas inválidas
- **Método**: Validação da estrutura JSON e campos obrigatórios

#### Fila de Processamento Inconsistente
- **Causa**: Falhas na remoção de jobs após conclusão
- **Solução**: Método melhorado de remoção com fallback por ID
- **Método**: Tentativa direta + busca por ID + remoção por índice

### Logs de Limpeza

O sistema gera logs detalhados sobre as operações de limpeza:

```
2024-01-15T12:30:00Z Limpeza: 2 jobs expirados removidos da fila de processamento
2024-01-15T12:35:00Z Limpeza de órfãos: 1 jobs inválidos removidos, 5 jobs válidos mantidos
2024-01-15T12:40:00Z Job job_abc123 removido da fila de processamento (método por ID)
```

## 📊 Métricas Disponíveis

### Métricas de Jobs
- Jobs enfileirados
- Jobs processados
- Taxa de sucesso
- Tempo médio de processamento
- Throughput (jobs/minuto)

### Métricas de Workers
- Workers ativos
- Tempo de processamento por worker
- Jobs processados por worker
- Uptime do pool

### Métricas de Erros
- Erros por tipo
- Taxa de falhas
- Jobs na dead letter queue

## 🐳 Docker Compose

O sistema inclui Redis automaticamente:

```yaml
services:
  redis:
    image: redis:7-alpine
    container_name: apexstream-redis
    ports:
      - "6379:6379"
    volumes:
      - apexstream_redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    
  apexstream:
    # ... configuração existente
    depends_on:
      redis:
        condition: service_healthy
    environment:
      - REDIS_HOST=redis
      - ENABLE_QUEUE=true
      - WORKER_POOL_SIZE=3
```

## 🔒 Segurança

- Todas as APIs de fila requerem autenticação via API key
- Jobs contêm metadados do cliente (IP, User-Agent)
- Logs estruturados para auditoria
- Isolamento de processamento por worker

## 🚨 Troubleshooting

### Job Travado em "processing"

**Sintomas**:
- Estatísticas mostram jobs em "processing" mas nenhum worker ativo
- Jobs não progridem por muito tempo

**Diagnóstico**:
1. Verificar workers ativos: `GET /api/v1/workers/stats`
2. Verificar estatísticas das filas: `GET /api/v1/queue/stats`
3. Verificar logs do sistema

**Soluções**:
1. **Automática**: O sistema de limpeza resolve automaticamente em até 1 minuto
2. **Manual**: Forçar limpeza: `POST /api/v1/queue/cleanup?type=expired`
3. **Emergencial**: Reiniciar o sistema (jobs são preservados no Redis)

### Estatísticas Incorretas

**Sintomas**:
- Contadores não batem com a realidade
- Jobs "fantasma" nas estatísticas

**Soluções**:
1. **Limpeza de órfãos**: `POST /api/v1/queue/cleanup?type=orphaned`
2. **Limpeza completa**: `POST /api/v1/queue/cleanup?type=all`
3. **Verificar logs**: Procurar por erros de serialização/deserialização

### Redis Indisponível
- Sistema fallback para processamento síncrono
- Health check indica status do Redis
- Reconexão automática

### Performance
- Ajustar `WORKER_POOL_SIZE` baseado no CPU
- Monitorar `JOB_TIMEOUT` para jobs longos
- Usar filas de prioridade para jobs urgentes
- Ajustar `QUEUE_CLEANUP_INTERVAL` se necessário

## 📝 Logs Estruturados

O sistema gera logs estruturados em JSON:

```json
{
  "timestamp": "2024-01-15T12:30:45Z",
  "level": "INFO",
  "message": "Worker concluiu job com sucesso",
  "job_id": "job_abc123",
  "worker_id": "worker-1",
  "duration": "45s",
  "metadata": {
    "filename": "video.mp4",
    "file_size": 52428800,
    "segments": 12,
    "storage_used": true
  }
}
```

## 🔍 Monitoramento e Alertas

### Indicadores de Saúde do Sistema

**Métricas Críticas para Monitorar**:

1. **Jobs em Processing**: Não deve crescer indefinidamente
   ```bash
   # Verificar se há jobs presos
   curl -s "http://localhost:8080/api/v1/queue/stats" | jq '.stats.processing'
   ```

2. **Workers Ativos**: Deve corresponder ao pool configurado
   ```bash
   # Verificar workers ativos
   curl -s "http://localhost:8080/api/v1/workers/stats" | jq '.pool_stats.active_workers'
   ```

3. **Taxa de Falhas**: Monitorar jobs na dead letter queue
   ```bash
   # Verificar jobs falhados
   curl -s "http://localhost:8080/api/v1/queue/stats" | jq '.stats.dead_letter'
   ```

### Scripts de Monitoramento

**Script de Health Check**:
```bash
#!/bin/bash
# health_check.sh

ENDPOINT="http://localhost:8080/api/v1/health/queue"
RESPONSE=$(curl -s "$ENDPOINT")
SUCCESS=$(echo "$RESPONSE" | jq -r '.success')

if [ "$SUCCESS" != "true" ]; then
    echo "ALERT: Sistema de filas não está saudável"
    echo "$RESPONSE" | jq '.'
    exit 1
fi

echo "Sistema de filas operacional"
```

**Script de Limpeza Forçada**:
```bash
#!/bin/bash
# force_cleanup.sh

echo "Executando limpeza completa das filas..."
curl -X POST "http://localhost:8080/api/v1/queue/cleanup?type=all" \
     -H "Authorization: Bearer $API_KEY"
echo "Limpeza concluída"
```

## 🔮 Próximos Passos

- [ ] Dashboard web para monitoramento
- [ ] Webhooks para notificações
- [ ] Processamento de múltiplas resoluções
- [ ] Integração com CDN
- [ ] Métricas Prometheus/Grafana
- [ ] Alertas automáticos por email/Slack
- [ ] Backup automático de jobs críticos
