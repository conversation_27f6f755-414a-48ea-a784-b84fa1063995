package processor

import (
	"log"
	"path/filepath"
)

// ExampleUsage demonstra como usar o VideoProcessor
func ExampleUsage() {
	// Configuração personalizada (opcional)
	config := &ProcessorConfig{
		FFmpegPath:    "ffmpeg",                    // Caminho para FFmpeg
		OutputDir:     "./videos/processed",       // Diretório de saída
		TempDir:       "./videos/temp",            // Diretório temporário
		SegmentTime:   10,                         // 10 segundos por segmento
		PlaylistType:  "vod",                      // Video on Demand
		VideoCodec:    "libx264",                  // Codec H.264
		AudioCodec:    "aac",                      // Codec AAC
		VideoBitrates: []int{720, 480, 360},       // Resoluções múltiplas
	}
	
	// Criar processador
	processor := NewVideoProcessor(config)
	
	// Validar se FFmpeg está disponível
	if err := processor.ValidateFFmpeg(); err != nil {
		log.Fatalf("Erro na validação do FFmpeg: %v", err)
	}
	
	// Exemplo de processamento
	inputPath := "./videos/input_video.mp4"
	outputID := "video_123456"
	
	log.Printf("Iniciando processamento do vídeo: %s", inputPath)
	
	// Processar vídeo para HLS
	result, err := processor.ProcessToHLS(inputPath, outputID)
	if err != nil {
		log.Printf("Erro no processamento: %v", err)
		return
	}
	
	if result.Success {
		log.Printf("✅ Processamento concluído com sucesso!")
		log.Printf("📁 Playlist: %s", result.PlaylistPath)
		log.Printf("🎬 Segmentos: %d arquivos", len(result.SegmentPaths))
		log.Printf("⏱️  Duração: %.2f segundos", result.Duration)
		log.Printf("🚀 Tempo de processamento: %v", result.ProcessingTime)
		
		// Listar segmentos gerados
		log.Printf("📋 Segmentos gerados:")
		for i, segment := range result.SegmentPaths {
			log.Printf("  %d. %s", i+1, filepath.Base(segment))
		}
	} else {
		log.Printf("❌ Falha no processamento: %s", result.Error)
	}
	
	// Exemplo de consulta de informações de vídeo processado
	info, err := processor.GetProcessedVideoInfo(outputID)
	if err != nil {
		log.Printf("Erro ao obter informações: %v", err)
	} else {
		log.Printf("📊 Informações do vídeo processado:")
		log.Printf("  Playlist: %s", info.PlaylistPath)
		log.Printf("  Segmentos: %d", len(info.SegmentPaths))
	}
	
	// Limpeza de arquivos temporários (opcional)
	if err := processor.CleanupTempFiles(outputID); err != nil {
		log.Printf("Aviso: erro na limpeza: %v", err)
	}
}

// ProcessVideoWithDefaultConfig processa um vídeo usando configuração padrão
func ProcessVideoWithDefaultConfig(inputPath, outputID string) (*ProcessingResult, error) {
	processor := NewVideoProcessor(nil) // Usa configuração padrão
	
	// Validar FFmpeg
	if err := processor.ValidateFFmpeg(); err != nil {
		return nil, err
	}
	
	// Processar
	return processor.ProcessToHLS(inputPath, outputID)
}

// ProcessVideoAsync processa um vídeo de forma assíncrona
func ProcessVideoAsync(inputPath, outputID string, resultChan chan<- *ProcessingResult) {
	go func() {
		defer close(resultChan)
		
		processor := NewVideoProcessor(nil)
		
		if err := processor.ValidateFFmpeg(); err != nil {
			resultChan <- &ProcessingResult{
				Success: false,
				Error:   err.Error(),
			}
			return
		}
		
		result, err := processor.ProcessToHLS(inputPath, outputID)
		if err != nil {
			resultChan <- &ProcessingResult{
				Success: false,
				Error:   err.Error(),
			}
			return
		}
		
		resultChan <- result
	}()
}

// BatchProcessVideos processa múltiplos vídeos
func BatchProcessVideos(videos []struct{ InputPath, OutputID string }) []*ProcessingResult {
	processor := NewVideoProcessor(nil)
	
	if err := processor.ValidateFFmpeg(); err != nil {
		log.Printf("Erro na validação do FFmpeg: %v", err)
		return nil
	}
	
	var results []*ProcessingResult
	
	for _, video := range videos {
		log.Printf("Processando: %s -> %s", video.InputPath, video.OutputID)
		
		result, err := processor.ProcessToHLS(video.InputPath, video.OutputID)
		if err != nil {
			result = &ProcessingResult{
				Success: false,
				Error:   err.Error(),
			}
		}
		
		results = append(results, result)
		
		if result.Success {
			log.Printf("✅ Sucesso: %s", video.OutputID)
		} else {
			log.Printf("❌ Falha: %s - %s", video.OutputID, result.Error)
		}
	}
	
	return results
}
