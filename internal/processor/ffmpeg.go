package processor

import (
	"bufio"
	"fmt"
	"io"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// ProgressCallback é uma função callback para reportar progresso
type ProgressCallback func(progress float64, message string)

type ProcessorConfig struct {
	FFmpegPath    string // Caminho para o executável do FFmpeg
	OutputDir     string // Diretório de saída para arquivos processados
	TempDir       string // Diretório temporário para processamento
	SegmentTime   int    // Duração de cada segmento HLS em segundos
	PlaylistType  string // Tipo de playlist HLS (vod ou live)
	VideoCodec    string // Codec de vídeo (h264, etc.)
	AudioCodec    string // Codec de áudio (aac, etc.)
	VideoBitrates []int  // Bitrates para streaming adaptativo
}

type ProcessingResult struct {
	Success        bool          `json:"success"`
	PlaylistPath   string        `json:"playlist_path"`
	SegmentPaths   []string      `json:"segment_paths"`
	Duration       float64       `json:"duration"`
	ProcessingTime time.Duration `json:"processing_time"`
	Error          string        `json:"error,omitempty"`
}

type VideoProcessor struct {
	config *ProcessorConfig
}

func NewVideoProcessor(config *ProcessorConfig) *VideoProcessor {
	if config == nil {
		config = getDefaultConfig()
	}

	if config.FFmpegPath == "" {
		config.FFmpegPath = "ffmpeg" // Assume que está no PATH
	}

	return &VideoProcessor{
		config: config,
	}
}

func getDefaultConfig() *ProcessorConfig {
	return &ProcessorConfig{
		FFmpegPath:    "ffmpeg",
		OutputDir:     "./videos/processed",
		TempDir:       "./videos/temp",
		SegmentTime:   10, // 10 segundos por segmento
		PlaylistType:  "vod",
		VideoCodec:    "libx264",
		AudioCodec:    "aac",
		VideoBitrates: []int{720, 480, 360}, // Resoluções para streaming adaptativo
	}
}

func (vp *VideoProcessor) ProcessToHLS(inputPath, outputID string) (*ProcessingResult, error) {
	return vp.ProcessToHLSWithProgress(inputPath, outputID, nil)
}

func (vp *VideoProcessor) ProcessToHLSWithProgress(inputPath, outputID string, progressCallback ProgressCallback) (*ProcessingResult, error) {
	startTime := time.Now()

	log.Printf("Iniciando processamento HLS para: %s (ID: %s)", inputPath, outputID)

	if _, err := os.Stat(inputPath); os.IsNotExist(err) {
		return &ProcessingResult{
			Success: false,
			Error:   fmt.Sprintf("arquivo de entrada não encontrado: %s", inputPath),
		}, err
	}

	if err := vp.createDirectories(outputID); err != nil {
		return &ProcessingResult{
			Success: false,
			Error:   fmt.Sprintf("erro ao criar diretórios: %v", err),
		}, err
	}

	// Obter duração do vídeo primeiro para calcular progresso
	duration, err := vp.getVideoDuration(inputPath)
	if err != nil {
		log.Printf("Aviso: não foi possível obter duração do vídeo: %v", err)
		duration = 0
	}

	outputDir := filepath.Join(vp.config.OutputDir, outputID)
	playlistPath := filepath.Join(outputDir, "playlist.m3u8")
	segmentPattern := filepath.Join(outputDir, "segment_%03d.ts")

	cmd := vp.buildFFmpegCommand(inputPath, playlistPath, segmentPattern)

	log.Printf("Executando comando FFmpeg: %s", strings.Join(cmd.Args, " "))

	// Executar FFmpeg com monitoramento de progresso
	err = vp.runFFmpegWithProgress(cmd, duration, progressCallback)
	if err != nil {
		return &ProcessingResult{
			Success: false,
			Error:   fmt.Sprintf("erro no processamento FFmpeg: %v", err),
		}, err
	}

	segmentPaths, err := vp.listGeneratedSegments(outputDir)
	if err != nil {
		log.Printf("Aviso: erro ao listar segmentos: %v", err)
	}

	processingTime := time.Since(startTime)

	result := &ProcessingResult{
		Success:        true,
		PlaylistPath:   playlistPath,
		SegmentPaths:   segmentPaths,
		Duration:       duration,
		ProcessingTime: processingTime,
	}

	log.Printf("Processamento HLS concluído com sucesso em %v", processingTime)

	return result, nil
}

func (vp *VideoProcessor) buildFFmpegCommand(inputPath, playlistPath, segmentPattern string) *exec.Cmd {
	args := []string{
		"-i", inputPath, // Arquivo de entrada
		"-c:v", vp.config.VideoCodec, // Codec de vídeo
		"-c:a", vp.config.AudioCodec, // Codec de áudio
		"-hls_time", fmt.Sprintf("%d", vp.config.SegmentTime), // Duração dos segmentos
		"-hls_playlist_type", vp.config.PlaylistType, // Tipo de playlist
		"-hls_segment_filename", segmentPattern, // Padrão dos segmentos
		"-f", "hls", // Formato de saída HLS
		"-y",         // Sobrescrever arquivos existentes
		playlistPath, // Arquivo de playlist de saída
	}

	return exec.Command(vp.config.FFmpegPath, args...)
}

func (vp *VideoProcessor) createDirectories(outputID string) error {
	outputDir := filepath.Join(vp.config.OutputDir, outputID)

	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("erro ao criar diretório de saída: %v", err)
	}

	if err := os.MkdirAll(vp.config.TempDir, 0755); err != nil {
		return fmt.Errorf("erro ao criar diretório temporário: %v", err)
	}

	return nil
}

func (vp *VideoProcessor) getVideoDuration(inputPath string) (float64, error) {
	cmd := exec.Command("ffprobe",
		"-v", "quiet",
		"-show_entries", "format=duration",
		"-of", "csv=p=0",
		inputPath,
	)

	output, err := cmd.Output()
	if err != nil {
		return 0, err
	}

	var duration float64
	if _, err := fmt.Sscanf(strings.TrimSpace(string(output)), "%f", &duration); err != nil {
		return 0, err
	}

	return duration, nil
}

func (vp *VideoProcessor) listGeneratedSegments(outputDir string) ([]string, error) {
	var segments []string

	files, err := os.ReadDir(outputDir)
	if err != nil {
		return nil, err
	}

	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".ts") {
			segments = append(segments, filepath.Join(outputDir, file.Name()))
		}
	}

	return segments, nil
}

func (vp *VideoProcessor) ValidateFFmpeg() error {
	cmd := exec.Command(vp.config.FFmpegPath, "-version")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("FFmpeg não encontrado ou não executável: %v", err)
	}

	log.Printf("FFmpeg validado com sucesso")
	return nil
}

func (vp *VideoProcessor) CleanupTempFiles(outputID string) error {
	tempDir := filepath.Join(vp.config.TempDir, outputID)

	if _, err := os.Stat(tempDir); os.IsNotExist(err) {
		return nil // Diretório não existe, nada para limpar
	}

	if err := os.RemoveAll(tempDir); err != nil {
		return fmt.Errorf("erro ao limpar arquivos temporários: %v", err)
	}

	log.Printf("Arquivos temporários limpos para ID: %s", outputID)
	return nil
}

func (vp *VideoProcessor) GetProcessedVideoInfo(outputID string) (*ProcessingResult, error) {
	outputDir := filepath.Join(vp.config.OutputDir, outputID)
	playlistPath := filepath.Join(outputDir, "playlist.m3u8")

	if _, err := os.Stat(playlistPath); os.IsNotExist(err) {
		return &ProcessingResult{
			Success: false,
			Error:   "vídeo processado não encontrado",
		}, err
	}

	segmentPaths, err := vp.listGeneratedSegments(outputDir)
	if err != nil {
		return &ProcessingResult{
			Success: false,
			Error:   fmt.Sprintf("erro ao listar segmentos: %v", err),
		}, err
	}

	return &ProcessingResult{
		Success:      true,
		PlaylistPath: playlistPath,
		SegmentPaths: segmentPaths,
	}, nil
}

// runFFmpegWithProgress executa FFmpeg e monitora o progresso
func (vp *VideoProcessor) runFFmpegWithProgress(cmd *exec.Cmd, totalDuration float64, progressCallback ProgressCallback) error {
	// Adicionar flags para obter informações de progresso do FFmpeg
	cmd.Args = append(cmd.Args[:len(cmd.Args)-1], "-progress", "pipe:2", cmd.Args[len(cmd.Args)-1])

	stderr, err := cmd.StderrPipe()
	if err != nil {
		return fmt.Errorf("erro ao criar pipe stderr: %v", err)
	}

	if err := cmd.Start(); err != nil {
		return fmt.Errorf("erro ao iniciar FFmpeg: %v", err)
	}

	// Monitorar progresso se callback foi fornecido
	if progressCallback != nil && totalDuration > 0 {
		go vp.monitorProgress(stderr, totalDuration, progressCallback)
	}

	return cmd.Wait()
}

// monitorProgress monitora a saída do FFmpeg para extrair informações de progresso
func (vp *VideoProcessor) monitorProgress(stderr io.ReadCloser, totalDuration float64, progressCallback ProgressCallback) {
	defer stderr.Close()

	scanner := bufio.NewScanner(stderr)
	timeRegex := regexp.MustCompile(`out_time_ms=(\d+)`)

	for scanner.Scan() {
		line := scanner.Text()

		// Procurar por informações de tempo no formato out_time_ms
		if matches := timeRegex.FindStringSubmatch(line); len(matches) > 1 {
			if timeMicros, err := strconv.ParseInt(matches[1], 10, 64); err == nil {
				currentTime := float64(timeMicros) / 1000000.0 // Converter de microssegundos para segundos

				if totalDuration > 0 {
					progress := (currentTime / totalDuration) * 100
					if progress > 100 {
						progress = 100
					}

					message := fmt.Sprintf("Processando: %.1fs de %.1fs", currentTime, totalDuration)
					progressCallback(progress, message)
				}
			}
		}
	}

	if err := scanner.Err(); err != nil {
		log.Printf("Erro ao ler saída do FFmpeg: %v", err)
	}
}
