package processor

import (
	"os"
	"path/filepath"
	"testing"
	"time"
)

func TestNewVideoProcessor(t *testing.T) {
	// Teste com configuração nil (deve usar padrão)
	processor := NewVideoProcessor(nil)
	if processor == nil {
		t.Fatal("NewVideoProcessor retornou nil")
	}
	
	if processor.config.FFmpegPath == "" {
		t.<PERSON>r("FFmpegPath não deve estar vazio")
	}
	
	if processor.config.SegmentTime <= 0 {
		t.Error("SegmentTime deve ser maior que zero")
	}
	
	// Teste com configuração personalizada
	customConfig := &ProcessorConfig{
		FFmpegPath:   "/usr/bin/ffmpeg",
		OutputDir:    "./test_output",
		SegmentTime:  5,
		PlaylistType: "live",
	}
	
	processor2 := NewVideoProcessor(customConfig)
	if processor2.config.FFmpegPath != "/usr/bin/ffmpeg" {
		t.<PERSON><PERSON>("Configuração personalizada não foi aplicada")
	}
}

func TestGetDefaultConfig(t *testing.T) {
	config := getDefaultConfig()
	
	if config.FFmpegPath != "ffmpeg" {
		t.Errorf("FFmpegPath padrão esperado 'ffmpeg', obtido '%s'", config.FFmpegPath)
	}
	
	if config.SegmentTime != 10 {
		t.Errorf("SegmentTime padrão esperado 10, obtido %d", config.SegmentTime)
	}
	
	if config.PlaylistType != "vod" {
		t.Errorf("PlaylistType padrão esperado 'vod', obtido '%s'", config.PlaylistType)
	}
	
	if len(config.VideoBitrates) == 0 {
		t.Error("VideoBitrates não deve estar vazio")
	}
}

func TestCreateDirectories(t *testing.T) {
	processor := NewVideoProcessor(nil)
	
	// Usar diretório temporário para teste
	tempDir := t.TempDir()
	processor.config.OutputDir = filepath.Join(tempDir, "output")
	processor.config.TempDir = filepath.Join(tempDir, "temp")
	
	outputID := "test_video_123"
	
	err := processor.createDirectories(outputID)
	if err != nil {
		t.Fatalf("Erro ao criar diretórios: %v", err)
	}
	
	// Verificar se os diretórios foram criados
	outputDir := filepath.Join(processor.config.OutputDir, outputID)
	if _, err := os.Stat(outputDir); os.IsNotExist(err) {
		t.Error("Diretório de saída não foi criado")
	}
	
	if _, err := os.Stat(processor.config.TempDir); os.IsNotExist(err) {
		t.Error("Diretório temporário não foi criado")
	}
}

func TestBuildFFmpegCommand(t *testing.T) {
	processor := NewVideoProcessor(nil)
	
	inputPath := "/path/to/input.mp4"
	playlistPath := "/path/to/output/playlist.m3u8"
	segmentPattern := "/path/to/output/segment_%03d.ts"
	
	cmd := processor.buildFFmpegCommand(inputPath, playlistPath, segmentPattern)
	
	if cmd.Path != "ffmpeg" {
		t.Errorf("Comando esperado 'ffmpeg', obtido '%s'", cmd.Path)
	}
	
	args := cmd.Args
	
	// Verificar argumentos essenciais
	expectedArgs := []string{
		"ffmpeg",
		"-i", inputPath,
		"-c:v", "libx264",
		"-c:a", "aac",
		"-hls_time", "10",
		"-hls_playlist_type", "vod",
		"-hls_segment_filename", segmentPattern,
		"-f", "hls",
		"-y",
		playlistPath,
	}
	
	if len(args) != len(expectedArgs) {
		t.Errorf("Número de argumentos esperado %d, obtido %d", len(expectedArgs), len(args))
	}
	
	for i, expected := range expectedArgs {
		if i < len(args) && args[i] != expected {
			t.Errorf("Argumento %d esperado '%s', obtido '%s'", i, expected, args[i])
		}
	}
}

func TestListGeneratedSegments(t *testing.T) {
	processor := NewVideoProcessor(nil)
	
	// Criar diretório temporário para teste
	tempDir := t.TempDir()
	
	// Criar alguns arquivos de teste
	testFiles := []string{
		"segment_001.ts",
		"segment_002.ts",
		"segment_003.ts",
		"playlist.m3u8",
		"other_file.txt",
	}
	
	for _, filename := range testFiles {
		filePath := filepath.Join(tempDir, filename)
		file, err := os.Create(filePath)
		if err != nil {
			t.Fatalf("Erro ao criar arquivo de teste: %v", err)
		}
		file.Close()
	}
	
	segments, err := processor.listGeneratedSegments(tempDir)
	if err != nil {
		t.Fatalf("Erro ao listar segmentos: %v", err)
	}
	
	// Deve encontrar apenas os arquivos .ts
	expectedCount := 3
	if len(segments) != expectedCount {
		t.Errorf("Esperado %d segmentos, encontrado %d", expectedCount, len(segments))
	}
	
	// Verificar se todos os segmentos terminam com .ts
	for _, segment := range segments {
		if !filepath.IsAbs(segment) {
			t.Error("Caminho do segmento deve ser absoluto")
		}
		
		if filepath.Ext(segment) != ".ts" {
			t.Errorf("Segmento deve ter extensão .ts, obtido: %s", segment)
		}
	}
}

func TestProcessingResult(t *testing.T) {
	result := &ProcessingResult{
		Success:        true,
		PlaylistPath:   "/path/to/playlist.m3u8",
		SegmentPaths:   []string{"/path/to/seg1.ts", "/path/to/seg2.ts"},
		Duration:       120.5,
		ProcessingTime: 30 * time.Second,
	}
	
	if !result.Success {
		t.Error("Result.Success deve ser true")
	}
	
	if result.PlaylistPath == "" {
		t.Error("PlaylistPath não deve estar vazio")
	}
	
	if len(result.SegmentPaths) != 2 {
		t.Errorf("Esperado 2 segmentos, obtido %d", len(result.SegmentPaths))
	}
	
	if result.Duration != 120.5 {
		t.Errorf("Duração esperada 120.5, obtida %f", result.Duration)
	}
	
	if result.ProcessingTime != 30*time.Second {
		t.Errorf("Tempo de processamento esperado 30s, obtido %v", result.ProcessingTime)
	}
}

func TestCleanupTempFiles(t *testing.T) {
	processor := NewVideoProcessor(nil)
	
	// Usar diretório temporário para teste
	tempDir := t.TempDir()
	processor.config.TempDir = tempDir
	
	outputID := "test_cleanup_123"
	testDir := filepath.Join(tempDir, outputID)
	
	// Criar diretório e arquivo de teste
	err := os.MkdirAll(testDir, 0755)
	if err != nil {
		t.Fatalf("Erro ao criar diretório de teste: %v", err)
	}
	
	testFile := filepath.Join(testDir, "temp_file.txt")
	file, err := os.Create(testFile)
	if err != nil {
		t.Fatalf("Erro ao criar arquivo de teste: %v", err)
	}
	file.Close()
	
	// Verificar se o arquivo existe antes da limpeza
	if _, err := os.Stat(testFile); os.IsNotExist(err) {
		t.Fatal("Arquivo de teste não foi criado")
	}
	
	// Executar limpeza
	err = processor.CleanupTempFiles(outputID)
	if err != nil {
		t.Fatalf("Erro na limpeza: %v", err)
	}
	
	// Verificar se o diretório foi removido
	if _, err := os.Stat(testDir); !os.IsNotExist(err) {
		t.Error("Diretório temporário não foi removido")
	}
}

func TestProcessVideoWithDefaultConfig(t *testing.T) {
	// Este teste só pode ser executado se o FFmpeg estiver instalado
	// Por isso, vamos apenas testar se a função não entra em pânico
	
	inputPath := "nonexistent_file.mp4"
	outputID := "test_123"
	
	result, err := ProcessVideoWithDefaultConfig(inputPath, outputID)
	
	// Esperamos um erro porque o arquivo não existe
	if err == nil {
		t.Error("Esperado erro para arquivo inexistente")
	}
	
	if result != nil && result.Success {
		t.Error("Resultado não deveria ser sucesso para arquivo inexistente")
	}
}

func TestProcessVideoAsync(t *testing.T) {
	inputPath := "nonexistent_file.mp4"
	outputID := "test_async_123"
	
	resultChan := make(chan *ProcessingResult, 1)
	
	ProcessVideoAsync(inputPath, outputID, resultChan)
	
	// Aguardar resultado
	select {
	case result := <-resultChan:
		if result == nil {
			t.Fatal("Resultado não deve ser nil")
		}
		
		if result.Success {
			t.Error("Resultado não deveria ser sucesso para arquivo inexistente")
		}
		
		if result.Error == "" {
			t.Error("Erro deve estar preenchido")
		}
		
	case <-time.After(5 * time.Second):
		t.Fatal("Timeout aguardando resultado assíncrono")
	}
}
