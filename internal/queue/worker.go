package queue

import (
	"context"
	"fmt"
	"log"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"

	"apexstream/internal/processor"
	"apexstream/internal/storage"
)

// WorkerConfig contém configurações do worker pool
type WorkerConfig struct {
	PoolSize          int           `json:"pool_size"`
	JobTimeout        time.Duration `json:"job_timeout"`
	HeartbeatInterval time.Duration `json:"heartbeat_interval"`
	ShutdownTimeout   time.Duration `json:"shutdown_timeout"`
}

// Worker representa um worker individual
type Worker struct {
	ID              string
	queueManager    *QueueManager
	videoProcessor  *processor.VideoProcessor
	storageProvider storage.StorageProvider
	config          *WorkerConfig
	ctx             context.Context
	cancel          context.CancelFunc
	wg              *sync.WaitGroup
	stats           *WorkerStats
	pool            *WorkerPool
}

// WorkerStats contém estatísticas do worker
type WorkerStats struct {
	JobsProcessed  int64         `json:"jobs_processed"`
	JobsSucceeded  int64         `json:"jobs_succeeded"`
	JobsFailed     int64         `json:"jobs_failed"`
	LastJobAt      time.Time     `json:"last_job_at"`
	StartedAt      time.Time     `json:"started_at"`
	IsActive       bool          `json:"is_active"`
	CurrentJobID   string        `json:"current_job_id"`
	ProcessingTime time.Duration `json:"processing_time"`
}

// ProgressReporter interface para reportar progresso
type ProgressReporter interface {
	SendProgressUpdate(jobID string, progress float64, status, message string, data map[string]interface{})
}

// WorkerPool gerencia um pool de workers
type WorkerPool struct {
	workers          []*Worker
	queueManager     *QueueManager
	videoProcessor   *processor.VideoProcessor
	storageProvider  storage.StorageProvider
	config           *WorkerConfig
	ctx              context.Context
	cancel           context.CancelFunc
	wg               sync.WaitGroup
	stats            *PoolStats
	mu               sync.RWMutex
	metrics          *MetricsCollector
	logger           *Logger
	progressReporter ProgressReporter // Para reportar progresso via WebSocket
}

// PoolStats contém estatísticas do pool
type PoolStats struct {
	TotalWorkers   int           `json:"total_workers"`
	ActiveWorkers  int           `json:"active_workers"`
	TotalProcessed int64         `json:"total_processed"`
	TotalSucceeded int64         `json:"total_succeeded"`
	TotalFailed    int64         `json:"total_failed"`
	StartedAt      time.Time     `json:"started_at"`
	Uptime         time.Duration `json:"uptime"`
}

// NewWorkerPool cria um novo pool de workers
func NewWorkerPool(queueManager *QueueManager, videoProcessor *processor.VideoProcessor,
	storageProvider storage.StorageProvider, config *WorkerConfig) *WorkerPool {

	if config == nil {
		config = &WorkerConfig{
			PoolSize:          runtime.NumCPU(),
			JobTimeout:        300 * time.Second, // 5 minutos
			HeartbeatInterval: 30 * time.Second,
			ShutdownTimeout:   60 * time.Second,
		}
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &WorkerPool{
		queueManager:    queueManager,
		videoProcessor:  videoProcessor,
		storageProvider: storageProvider,
		config:          config,
		ctx:             ctx,
		cancel:          cancel,
		stats: &PoolStats{
			TotalWorkers: config.PoolSize,
			StartedAt:    time.Now(),
		},
		metrics: NewMetricsCollector(),
		logger:  NewLogger(),
	}
}

// SetProgressReporter define o reporter de progresso para WebSocket
func (wp *WorkerPool) SetProgressReporter(reporter ProgressReporter) {
	wp.progressReporter = reporter
}

// Start inicia o pool de workers
func (wp *WorkerPool) Start() error {
	log.Printf("Iniciando worker pool com %d workers", wp.config.PoolSize)

	wp.workers = make([]*Worker, wp.config.PoolSize)

	for i := 0; i < wp.config.PoolSize; i++ {
		worker := wp.createWorker(fmt.Sprintf("worker-%d", i+1))
		wp.workers[i] = worker

		wp.wg.Add(1)
		go worker.start()
	}

	// Iniciar worker de limpeza
	wp.wg.Add(1)
	go wp.queueManager.StartCleanupWorker(wp.ctx)

	log.Printf("Worker pool iniciado com sucesso")
	return nil
}

// Stop para o pool de workers gracefully
func (wp *WorkerPool) Stop() error {
	log.Println("Parando worker pool...")

	// Cancelar contexto para sinalizar parada
	wp.cancel()

	// Aguardar workers terminarem com timeout
	done := make(chan struct{})
	go func() {
		wp.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		log.Println("Todos os workers foram finalizados")
	case <-time.After(wp.config.ShutdownTimeout):
		log.Printf("Timeout de %v atingido, forçando parada", wp.config.ShutdownTimeout)
	}

	return nil
}

// GetStats retorna estatísticas do pool
func (wp *WorkerPool) GetStats() *PoolStats {
	wp.mu.RLock()
	defer wp.mu.RUnlock()

	stats := *wp.stats
	stats.Uptime = time.Since(stats.StartedAt)

	// Contar workers ativos
	activeWorkers := 0
	for _, worker := range wp.workers {
		if worker.stats.IsActive {
			activeWorkers++
		}
	}
	stats.ActiveWorkers = activeWorkers

	return &stats
}

// GetWorkerStats retorna estatísticas de todos os workers
func (wp *WorkerPool) GetWorkerStats() []*WorkerStats {
	wp.mu.RLock()
	defer wp.mu.RUnlock()

	stats := make([]*WorkerStats, len(wp.workers))
	for i, worker := range wp.workers {
		workerStats := *worker.stats
		stats[i] = &workerStats
	}

	return stats
}

// createWorker cria um novo worker
func (wp *WorkerPool) createWorker(id string) *Worker {
	ctx, cancel := context.WithCancel(wp.ctx)

	return &Worker{
		ID:              id,
		queueManager:    wp.queueManager,
		videoProcessor:  wp.videoProcessor,
		storageProvider: wp.storageProvider,
		config:          wp.config,
		ctx:             ctx,
		cancel:          cancel,
		wg:              &wp.wg,
		pool:            wp,
		stats: &WorkerStats{
			StartedAt: time.Now(),
			IsActive:  false,
		},
	}
}

// start inicia o loop principal do worker
func (w *Worker) start() {
	defer w.wg.Done()
	defer w.cancel()

	log.Printf("Worker %s iniciado", w.ID)

	// Heartbeat ticker
	heartbeat := time.NewTicker(w.config.HeartbeatInterval)
	defer heartbeat.Stop()

	for {
		select {
		case <-w.ctx.Done():
			log.Printf("Worker %s finalizando...", w.ID)
			return

		case <-heartbeat.C:
			// Heartbeat - pode ser usado para monitoramento
			continue

		default:
			// Tentar processar um job
			if err := w.processNextJob(); err != nil {
				if err.Error() != "no jobs available" {
					log.Printf("Worker %s erro: %v", w.ID, err)
				}
				// Aguardar um pouco antes de tentar novamente
				time.Sleep(time.Second)
			}
		}
	}
}

// processNextJob processa o próximo job da fila
func (w *Worker) processNextJob() error {
	// Criar contexto com timeout para o job
	jobCtx, cancel := context.WithTimeout(w.ctx, w.config.JobTimeout)
	defer cancel()

	// Obter próximo job da fila
	job, err := w.queueManager.Dequeue(jobCtx)
	if err != nil {
		return fmt.Errorf("erro ao obter job da fila: %v", err)
	}

	if job == nil {
		return fmt.Errorf("no jobs available")
	}

	// Atualizar estatísticas
	w.stats.IsActive = true
	w.stats.CurrentJobID = job.ID
	w.stats.LastJobAt = time.Now()
	w.stats.JobsProcessed++

	// Definir worker no job
	job.SetWorker(w.ID)

	log.Printf("Worker %s processando job %s", w.ID, job.ID)

	// Processar job
	startTime := time.Now()
	err = w.processVideoJob(jobCtx, job)
	processingTime := time.Since(startTime)

	// Atualizar estatísticas
	w.stats.ProcessingTime += processingTime
	w.stats.IsActive = false
	w.stats.CurrentJobID = ""

	if err != nil {
		w.stats.JobsFailed++

		// Registrar métricas e logs
		if w.pool != nil {
			w.pool.metrics.RecordJobProcessed(processingTime, false)
			w.pool.metrics.RecordError("processing_failed")
			w.pool.logger.LogError("Worker falhou ao processar job", job.ID, w.ID, err, map[string]interface{}{
				"processing_time": processingTime.String(),
				"filename":        job.Metadata.OriginalName,
			})
		}

		log.Printf("Worker %s falhou ao processar job %s: %v", w.ID, job.ID, err)

		// Marcar job como falhado
		if failErr := w.queueManager.FailJob(jobCtx, job, err); failErr != nil {
			log.Printf("Erro ao marcar job como falhado: %v", failErr)
		}

		return err
	}

	w.stats.JobsSucceeded++

	// Registrar métricas e logs de sucesso
	if w.pool != nil {
		w.pool.metrics.RecordJobProcessed(processingTime, true)
		w.pool.metrics.RecordProcessingTimeByType("video_processing", processingTime)
		w.pool.logger.LogPerformance("Worker concluiu job com sucesso", job.ID, w.ID, processingTime, map[string]interface{}{
			"filename":     job.Metadata.OriginalName,
			"file_size":    job.Metadata.FileSize,
			"segments":     job.Result.SegmentCount,
			"duration":     job.Result.Duration,
			"storage_used": job.Result.StorageUploaded,
		})
	}

	log.Printf("Worker %s concluiu job %s em %v", w.ID, job.ID, processingTime)

	// Marcar job como concluído
	if completeErr := w.queueManager.CompleteJob(jobCtx, job); completeErr != nil {
		log.Printf("Erro ao marcar job como concluído: %v", completeErr)
	}

	return nil
}

// processVideoJob processa um job de vídeo específico
func (w *Worker) processVideoJob(ctx context.Context, job *VideoJob) error {
	// Verificar se o arquivo de entrada existe
	if job.InputFile == "" {
		return fmt.Errorf("arquivo de entrada não especificado")
	}

	// Reportar início do processamento
	if w.pool.progressReporter != nil {
		w.pool.progressReporter.SendProgressUpdate(job.ID, 0, "processing", "Iniciando processamento de vídeo", nil)
	}

	// Criar callback de progresso
	progressCallback := func(progress float64, message string) {
		if w.pool.progressReporter != nil {
			w.pool.progressReporter.SendProgressUpdate(job.ID, progress, "processing", message, nil)
		}
	}

	// Processar vídeo com FFmpeg usando callback de progresso
	result, err := w.videoProcessor.ProcessToHLSWithProgress(job.InputFile, job.ID, progressCallback)
	if err != nil {
		return fmt.Errorf("erro no processamento FFmpeg: %v", err)
	}

	// Atualizar resultado do job
	job.Result.Success = result.Success
	job.Result.PlaylistPath = result.PlaylistPath
	job.Result.SegmentPaths = result.SegmentPaths
	job.Result.Duration = result.Duration
	job.Result.ProcessingTime = result.ProcessingTime
	job.Result.SegmentCount = len(result.SegmentPaths)

	// Reportar progresso de upload se storage está habilitado
	if job.Config.EnableStorage && w.storageProvider != nil {
		if w.pool.progressReporter != nil {
			w.pool.progressReporter.SendProgressUpdate(job.ID, 95, "uploading", "Fazendo upload dos arquivos", nil)
		}

		if err := w.uploadToStorage(ctx, job); err != nil {
			return fmt.Errorf("erro no upload para storage: %v", err)
		}
	}

	// Reportar conclusão
	if w.pool.progressReporter != nil {
		w.pool.progressReporter.SendProgressUpdate(job.ID, 100, "completed", "Processamento concluído com sucesso", map[string]interface{}{
			"duration":      result.Duration,
			"segments":      len(result.SegmentPaths),
			"playlist_path": result.PlaylistPath,
		})
	}

	return nil
}

// uploadToStorage faz upload dos arquivos processados para o storage
func (w *Worker) uploadToStorage(ctx context.Context, job *VideoJob) error {
	// Determinar diretório de saída baseado no resultado do processamento
	outputDir := filepath.Dir(job.Result.PlaylistPath)
	keyPrefix := fmt.Sprintf("videos/%s", job.ID)

	// Upload do diretório completo
	uploadResult, err := w.storageProvider.UploadDirectory(ctx, outputDir, keyPrefix)
	if err != nil {
		return err
	}

	// Atualizar resultado com URLs públicas
	job.Result.StorageUploaded = true
	job.Result.StorageProvider = job.Config.StorageType

	// Calcular tamanho total
	var totalSize int64
	for _, success := range uploadResult.Success {
		totalSize += success.Size
	}
	job.Result.FileSize = totalSize

	// Construir URLs públicas para playlist e segmentos
	for _, success := range uploadResult.Success {
		if strings.HasSuffix(success.Key, ".m3u8") {
			job.Result.PlaylistURL = success.URL
		} else if strings.HasSuffix(success.Key, ".ts") {
			job.Result.SegmentURLs = append(job.Result.SegmentURLs, success.URL)
		}
	}

	log.Printf("Upload concluído para job %s: %d arquivos, %d bytes",
		job.ID, uploadResult.SuccessCount, totalSize)

	return nil
}

// GetPoolMetrics retorna métricas do pool
func (wp *WorkerPool) GetPoolMetrics() *Metrics {
	return wp.metrics.GetMetrics()
}

// GetPoolLogger retorna o logger do pool
func (wp *WorkerPool) GetPoolLogger() *Logger {
	return wp.logger
}

// GetDetailedPoolMetrics retorna métricas detalhadas do pool
func (wp *WorkerPool) GetDetailedPoolMetrics() map[string]interface{} {
	poolStats := wp.GetStats()
	workerStats := wp.GetWorkerStats()
	metrics := wp.metrics.GetDetailedStats()

	return map[string]interface{}{
		"pool_stats":   poolStats,
		"worker_stats": workerStats,
		"metrics":      metrics,
		"recent_logs":  wp.logger.GetRecentEvents(50),
	}
}
